# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于 Spring Boot 的 ZTE CRM 商机信息管理系统，主要功能包括：
- 商机管理（机会管理、团队管理、产品管理）
- 客户信息管理
- AI 招标文档解析
- 审批流程管理
- 报表和导出功能

## 常用开发命令

### 构建和运行
```bash
# 清理并构建项目（跳过测试）
mvn clean package -Dmaven.test.skip=true

# 构建并安装到本地仓库（包含测试）
mvn clean install

# 运行应用
java -jar target/zte-crm-opportunity-info-1.1.1.jar

# 使用 Spring Boot 插件运行（支持热重载）
mvn clean spring-boot:run
```

### 测试命令
```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=SOpportunityServiceImplTest

# 生成测试覆盖率报告
mvn clean test jacoco:report
```

### 代码质量检查
```bash
# Sonar 扫描（需要配置 Sonar 服务器）
mvn sonar:sonar

# 编译检查
mvn compile
```

## 高层架构设计

### 1. 整体架构
项目采用分层架构，主要包含以下模块：

```
zte-crm-opportunity-info/
├── aiagent/          # AI 招标文档解析模块（DDD 架构）
├── crm/              # CRM 核心功能模块
├── leadinfo/         # 线索信息管理
├── mcrm/             # 微服务 CRM 模块
├── opty/             # 商机管理核心模块
└── springbootframe/  # Spring Boot 框架配置
```

### 2. AI Agent 模块（DDD 设计）
采用领域驱动设计，包含以下层次：
- **ui**: 控制器层，处理 HTTP 请求
- **application**: 应用服务层，编排业务流程
- **domain**: 领域层，核心业务逻辑
  - aggregate: 聚合根（如 BidDocument）
  - entity: 实体
  - valueobject: 值对象
  - repository: 仓储接口
  - service: 领域服务
- **infrastructure**: 基础设施层
  - access: 数据访问实现
  - adapter: 外部服务适配器

### 3. 商机管理模块（opty）
核心业务模块，包含：
- 商机信息管理（SOpportunity）
- 商机团队管理（SOptyTeam）
- 商机产品管理（SOptyProduct）
- 审批流程（Approval）
- 权限控制（Authorization）

### 4. 微服务集成
- 使用 Spring Cloud 进行服务注册和发现
- Feign 客户端进行服务间调用
- Kafka 消息队列处理异步任务
- Redis 缓存提升性能

## 关键技术栈

- **框架**: Spring Boot 2.x, Spring Cloud
- **ORM**: MyBatis, MyBatis-Plus 3.5.4
- **数据库**: MySQL (支持 GoldenDB)
- **消息队列**: Kafka
- **缓存**: Redis
- **构建工具**: Maven 3.x
- **Java版本**: JDK 1.8

## 数据库配置

项目支持多环境配置（dev/test/prod），主要数据源配置在：
- `src/main/resources/application.yml`
- `src/main/resources/register_${env}.properties`

## 重要配置文件

1. **application.yml**: Spring Boot 主配置
2. **bootstrap.yml**: Spring Cloud 配置
3. **logback-spring.xml**: 日志配置
4. **mybatis-config.xml**: MyBatis 配置
5. **各环境配置文件**: register_dev.properties 等

## 安全和认证

- 使用 ZTE UAC 统一认证中心
- Spring Security 进行权限控制
- 数据加密使用 AES 和 HMAC-SHA256

## 开发注意事项

1. **编码规范**: 使用 UTF-8 编码
2. **分页**: 使用 PageHelper 进行分页
3. **事务管理**: 使用 @Transactional 注解
4. **异常处理**: 统一异常处理机制
5. **日志**: 使用 SLF4J + Logback
6. **API文档**: 集成 Swagger2，访问路径 `/swagger-ui.html`

## 部署注意事项

1. **端口配置**: 默认端口 8080
2. **上下文路径**: `/zte-crm-opportunity-info/`
3. **文件上传限制**: 最大 200MB
4. **会话超时**: 3600 秒
5. **Tomcat 配置**: 最大线程数 2000

## 定时任务

- StartFlowTask: 每30分钟查询客户草稿
- MonthlyReportUpdateReminderTask: 每天早上8点月报提醒
- MonthlyOpportunityInvalidTask: 每月1号商机失效处理